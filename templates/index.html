{% extends "base.html" %} 
{% block title %} Jobsvider {% endblock %} 
{% block content%}

<!-- Hero Section -->
<section class="gradient-bg py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8 order-lg-1 order-2">
        <h1 class="display-4 fw-bold mb-4">
          Find Your Perfect<br />
          <span class="text-primary">Career Opportunity</span>
        </h1>
        <p class="lead text-muted mb-4">
          Discover top companies, explore ideal positions, and ensure your
          workplace expectations are met. Join our talent pool to receive
          curated offers from leading employers.
        </p>
        <div class="d-flex flex-wrap gap-3 mb-4">
          <a href="/jobs" class="btn btn-primary btn-lg px-4">
            Browse Jobs <i class="bi bi-search ms-2"></i>
          </a>
          <a href="/employers" class="btn btn-outline-primary btn-lg px-4">
            View Companies <i class="bi bi-building ms-2"></i>
          </a>
        </div>
        <p class="text-muted">
          Looking to hire?
          <a
            href="/employer-signup"
            class="text-decoration-none fw-semibold text-primary"
          >
            Create Employer Profile <i class="bi bi-arrow-right-short"></i>
          </a>
        </p>
      </div>
      <div class="col-lg-4 order-lg-2 order-1 mb-4 mb-lg-0 px-auto">
        <img
          src="./static/hero-icon.svg"
          alt="Career opportunities"
          class="img-fluid rounded mx-auto d-block hero-img"
        />
      </div>
    </div>
  </div>
</section>

<!-- Recent Remote Jobs Section -->
<section class="py-5">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="fs-1 fw-bold mb-0">
        <i class="bi bi-laptop me-3 text-primary"></i>
        Recent Opportunities
      </h2>
      <a href="/jobs" class="btn btn-outline-primary">
        View All Jobs <i class="bi bi-arrow-right ms-2"></i>
      </a>
    </div>

    <div class="row g-4">
      {% for vacancy in recent_remote_jobs %}
      <div class="col-lg-3 col-md-6">
        <a
          href="{{ url_for('view_vacancy', vacancy_id=vacancy.vacancy_id) }}"
          class="text-decoration-none text-dark"
        >
          <div class="job-card card h-100 shadow-sm hover-shadow border-0">
            <div class="card-body p-4">
              <div class="d-flex align-items-start mb-3">
                <div class="flex-shrink-0 me-3">
                  <img
                    src="{{ vacancy.employer_logo_url }}"
                    alt="{{ vacancy.employer_name }} logo"
                    class="rounded-3"
                    style="width: 50px; height: 50px; object-fit: contain;"
                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiByeD0iOCIgZmlsbD0iIzZjNzU3ZCIvPgo8dGV4dCB4PSIyNSIgeT0iMzIiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnt7dmFjYW5jeS5lbXBsb3llcl9uYW1lWzBdfX08L3RleHQ+Cjwvc3ZnPgo='"
                  />
                </div>
                <div class="flex-grow-1 min-w-0">
                  <h6 class="card-title mb-1 fw-bold text-truncate">{{ vacancy.vacancy_title }}</h6>
                  <p class="text-muted mb-0 small">{{ vacancy.employer_name }}</p>
                </div>
              </div>

              <div class="d-flex flex-wrap gap-2 mb-3">
                <span class="badge bg-primary bg-opacity-10 text-primary rounded-pill small">
                  <i class="bi bi-globe me-1"></i> {{ vacancy.office_schedule or 'On-site' }}
                </span>
                <span class="badge bg-secondary bg-opacity-10 text-secondary rounded-pill small">
                  <i class="bi bi-geo-alt me-1"></i> {{ vacancy.vacancy_country }}
                </span>
              </div>

              <div class="d-flex justify-content-between align-items-end">
                {% if vacancy.salary_min and vacancy.salary_max %}
                <div>
                  <div class="fw-bold text-success">
                    {{ vacancy.salary_currency }} {{ "{:,}".format(vacancy.salary_min) }} - {{ "{:,}".format(vacancy.salary_max) }}
                  </div>
                  <small class="text-muted">per month</small>
                </div>
                {% else %}
                <div>
                  <div class="text-muted">Salary not specified</div>
                </div>
                {% endif %}
                <small class="text-muted">
                  {{ vacancy.vacancy_age }}
                </small>
              </div>
            </div>
          </div>
        </a>
      </div>
      {% endfor %}
    </div>

    <!-- Show message if no jobs -->
    {% if not recent_remote_jobs %}
    <div class="text-center py-5">
      <i class="bi bi-briefcase display-4 text-muted mb-3"></i>
      <h5 class="text-muted">No recent jobs available</h5>
      <p class="text-muted">Check back later for new opportunities</p>
    </div>
    {% endif %}
  </div>
</section>

<!-- Highest Paying Jobs Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="fs-1 fw-bold mb-0">
        <i class="bi bi-graph-up me-3 text-success"></i>
        Top Paying Positions
      </h2>
      <a href="/jobs?sort=salary" class="btn btn-outline-success">
        View All High-Paying Jobs <i class="bi bi-arrow-right ms-2"></i>
      </a>
    </div>

    <div class="row g-4">
      {% for vacancy in highest_paying_jobs %}
      <div class="col-lg-3 col-md-6">
        <a
          href="{{ url_for('view_vacancy', vacancy_id=vacancy.vacancy_id) }}"
          class="text-decoration-none text-dark"
        >
          <div class="job-card card h-100 shadow-sm hover-shadow border-0 position-relative">
            <!-- Salary highlight badge -->
            <div class="position-absolute top-0 end-0 m-3">
              <span class="badge bg-success">
                <i class="bi bi-star-fill me-1"></i>Top Pay
              </span>
            </div>

            <div class="card-body p-4">
              <div class="d-flex align-items-start mb-3">
                <div class="flex-shrink-0 me-3">
                  <img
                    src="{{ vacancy.employer_logo_url }}"
                    alt="{{ vacancy.employer_name }} logo"
                    class="rounded-3"
                    style="width: 50px; height: 50px; object-fit: contain;"
                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiByeD0iOCIgZmlsbD0iIzZjNzU3ZCIvPgo8dGV4dCB4PSIyNSIgeT0iMzIiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnt7dmFjYW5jeS5lbXBsb3llcl9uYW1lWzBdfX08L3RleHQ+Cjwvc3ZnPgo='"
                  />
                </div>
                <div class="flex-grow-1 min-w-0">
                  <h6 class="card-title mb-1 fw-bold text-truncate">{{ vacancy.vacancy_title }}</h6>
                  <p class="text-muted mb-0 small">{{ vacancy.employer_name }}</p>
                </div>
              </div>

              <div class="d-flex flex-wrap gap-2 mb-3">
                <span class="badge bg-primary bg-opacity-10 text-primary rounded-pill small">
                  <i class="bi bi-globe me-1"></i> {{ vacancy.office_schedule or 'On-site' }}
                </span>
                <span class="badge bg-secondary bg-opacity-10 text-secondary rounded-pill small">
                  <i class="bi bi-geo-alt me-1"></i> {{ vacancy.vacancy_country }}
                </span>
              </div>

              <div class="d-flex justify-content-between align-items-end">
                <div>
                  <div class="fw-bold text-success fs-5">
                    {{ vacancy.salary_currency }} {{ "{:,}".format(vacancy.salary_min) }} - {{ "{:,}".format(vacancy.salary_max) }}
                  </div>
                  <small class="text-muted">per month</small>
                </div>
                <small class="text-muted">
                  {{ vacancy.vacancy_age }}
                </small>
              </div>
            </div>
          </div>
        </a>
      </div>
      {% endfor %}
    </div>

    <!-- Show message if no jobs -->
    {% if not highest_paying_jobs %}
    <div class="text-center py-5">
      <i class="bi bi-currency-dollar display-4 text-muted mb-3"></i>
      <h5 class="text-muted">No high-paying jobs available</h5>
      <p class="text-muted">Check back later for new opportunities</p>
    </div>
    {% endif %}
  </div>
</section>

<!-- Job Categories Section -->
<section class="py-5">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fs-1 fw-bold mb-3">
        <i class="bi bi-grid-3x3-gap me-3 text-info"></i>
        Explore by Category
      </h2>
      <p class="lead text-muted">Find opportunities in your field of expertise</p>
    </div>

    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <a href="/jobs?keyword=software+engineer" class="text-decoration-none">
          <div class="category-card card h-100 shadow-sm hover-shadow border-0 text-center">
            <div class="card-body p-4">
              <div class="category-icon mb-3">
                <i class="bi bi-code-slash display-4 text-primary"></i>
              </div>
              <h5 class="card-title fw-bold">Software Engineering</h5>
              <p class="text-muted small">Full-stack, Backend, Frontend, Mobile</p>
            </div>
          </div>
        </a>
      </div>

      <div class="col-lg-3 col-md-6">
        <a href="/jobs?keyword=data+scientist" class="text-decoration-none">
          <div class="category-card card h-100 shadow-sm hover-shadow border-0 text-center">
            <div class="card-body p-4">
              <div class="category-icon mb-3">
                <i class="bi bi-graph-up display-4 text-success"></i>
              </div>
              <h5 class="card-title fw-bold">Data Science</h5>
              <p class="text-muted small">Analytics, ML, AI, Research</p>
            </div>
          </div>
        </a>
      </div>

      <div class="col-lg-3 col-md-6">
        <a href="/jobs?keyword=product+manager" class="text-decoration-none">
          <div class="category-card card h-100 shadow-sm hover-shadow border-0 text-center">
            <div class="card-body p-4">
              <div class="category-icon mb-3">
                <i class="bi bi-kanban display-4 text-warning"></i>
              </div>
              <h5 class="card-title fw-bold">Product Management</h5>
              <p class="text-muted small">Strategy, Roadmap, Growth</p>
            </div>
          </div>
        </a>
      </div>

      <div class="col-lg-3 col-md-6">
        <a href="/jobs?keyword=designer" class="text-decoration-none">
          <div class="category-card card h-100 shadow-sm hover-shadow border-0 text-center">
            <div class="card-body p-4">
              <div class="category-icon mb-3">
                <i class="bi bi-palette display-4 text-info"></i>
              </div>
              <h5 class="card-title fw-bold">Design</h5>
              <p class="text-muted small">UI/UX, Graphic, Brand</p>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</section>

{% endblock %}
