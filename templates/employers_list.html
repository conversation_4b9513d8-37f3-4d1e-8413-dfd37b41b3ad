{% for employer in all_employers %}
<div class="col-xl-4 col-lg-6 col-md-6 col-12">
  <div class="employer-card">
    <div class="company-logo-wrapper">
      <div class="d-flex justify-content-between align-items-center">
        <img
          src="{{ employer.employer_logo_url }}"
          class="employer-icon-md"
          alt="{{ employer.employer_name }}"
        />
        <div class="open-positions">{{ employer.open_positions }} Openings</div>
      </div>
    </div>

    <div class="company-details">
      <h6 class="fw-bold mb-3">{{ employer.employer_name }}</h6>

      <div class="detail-item">
        <span class="detail-label">Industry</span>
        <span class="detail-value">{{ employer.employer_industry }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Employees</span>
        <span class="detail-value">{{ employer.employer_headcount }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Location</span>
        <span class="detail-value">{{ employer.headquarter }}</span>
      </div>
    </div>

    <a
      href="{{url_for('employer',employer_id=employer.employerid)}}"
      class="btn btn-outline-primary w-100 py-2 border-top rounded-0"
    >
      View Details →
    </a>
  </div>
</div>
{% endfor %}
